3. userName already exist -> user friendly message on Login screen
4. Edit Profile -> Update First Name & last name in dialog
5. On login & sugnup succes -> hit /api/db/clinics and then take to onboarding when nothing exists
6. Manage Clinics secytion missing -> Edit & Remove CTA -> On Edit take to edit clinic screen, remove "Go to admin panel"
7. Select first clinic by default and show on dashboard -> maintain in context -> selectedClinic
8. Clinic Dropdown, Populate Clinic Name
9. Day, Week, Month, integrate API, use <EMAIL> account, ask OTP
10. error.message in toast on every API call
11. Invite staff dialog -> stictch API
12. Manage Staffs -> Fetch all users , API: localhost:4000/api/db/clinic-owners?clinic_id=6821fde1a57e294a49fcbc4d
13. Auth Context: store access_token & id_token and use force refresh
