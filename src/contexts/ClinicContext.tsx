'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  ReactNode,
} from 'react';
import { fetchAuthSession } from 'aws-amplify/auth';
import { getUserClinics } from '@/services/clinicService';
import { ClinicType } from '@/lib/types';
import { toast } from 'sonner';

interface ClinicContextType {
  clinics: ClinicType[];
  selectedClinic: ClinicType | null;
  loading: boolean;
  error: string | null;
  setSelectedClinic: (clinic: ClinicType) => void;
  fetchClinics: () => Promise<void>;
  refreshClinics: () => Promise<void>;
}

const ClinicContext = createContext<ClinicContextType | undefined>(undefined);

interface ClinicProviderProps {
  children: ReactNode;
}

export function ClinicProvider({ children }: ClinicProviderProps) {
  const [clinics, setClinics] = useState<ClinicType[]>([]);
  const [selectedClinic, setSelectedClinicState] = useState<ClinicType | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchClinics = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const session = await fetchAuthSession();
      const accessToken = session.tokens?.accessToken?.toString();

      if (!accessToken) {
        throw new Error('No access token available');
      }

      const result = await getUserClinics(accessToken);

      if (!result.ok) {
        throw new Error(result.error || 'Failed to fetch clinics');
      }

      const fetchedClinics = result.data || [];
      setClinics(fetchedClinics);

      // Auto-select first clinic if none is selected and clinics exist
      if (fetchedClinics.length > 0 && !selectedClinic) {
        setSelectedClinicState(fetchedClinics[0]);
      }
    } catch (error) {
      console.error('Error fetching clinics:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch clinics';
      setError(errorMessage);
      toast.error('Failed to load clinics', {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [selectedClinic]);

  const refreshClinics = useCallback(async () => {
    await fetchClinics();
  }, [fetchClinics]);

  const setSelectedClinic = useCallback((clinic: ClinicType) => {
    setSelectedClinicState(clinic);
    // Store selected clinic in localStorage for persistence
    localStorage.setItem('selectedClinic', JSON.stringify(clinic));
  }, []);

  // Load selected clinic from localStorage on mount
  useEffect(() => {
    const storedClinic = localStorage.getItem('selectedClinic');
    if (storedClinic) {
      try {
        const parsedClinic = JSON.parse(storedClinic);
        setSelectedClinicState(parsedClinic);
      } catch (error) {
        console.error('Error parsing stored clinic:', error);
        localStorage.removeItem('selectedClinic');
      }
    }
  }, []);

  // Fetch clinics on mount
  useEffect(() => {
    fetchClinics();
  }, [fetchClinics]);

  return (
    <ClinicContext.Provider
      value={{
        clinics,
        selectedClinic,
        loading,
        error,
        setSelectedClinic,
        fetchClinics,
        refreshClinics,
      }}
    >
      {children}
    </ClinicContext.Provider>
  );
}

export function useClinic() {
  const context = useContext(ClinicContext);
  if (context === undefined) {
    throw new Error('useClinic must be used within a ClinicProvider');
  }
  return context;
}
