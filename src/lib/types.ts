export interface ClinicType {
  _id: string;
  clinic_name: string;
  clinic_addresses: string[];
  clinic_email: string;
  clinic_website: string;
  clinic_phone?: string;
  is_active: boolean;
  diagnostic_services: string[];
  created_at: string;
  updated_at: string;
  __v?: number;
  human_transfer_destination_number?: string;
  crm_details?: {
    name: string;
    auth_details?: Record<string, string>;
    custom_fields?: Record<string, string>;
  };
}
