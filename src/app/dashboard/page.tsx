'use client';

import { useState } from 'react';
import { Building2, Clock, Phone, Plus, Users, TrendingUp } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

// Sample data for charts
const callData = [
  { name: 'Mon', calls: 12 },
  { name: '<PERSON><PERSON>', calls: 19 },
  { name: 'Wed', calls: 15 },
  { name: 'Thu', calls: 22 },
  { name: 'Fri', calls: 18 },
  { name: 'Sat', calls: 8 },
  { name: '<PERSON>', calls: 5 },
];

const callTypeData = [
  { name: 'Answered', value: 75, fill: 'var(--color-answered)' },
  { name: 'Missed', value: 25, fill: 'var(--color-missed)' },
];

const callTypeChartConfig = {
  answered: {
    label: 'Answered',
    color: 'hsl(24, 95%, 50%)', // Vibrant Orange
  },
  missed: {
    label: 'Missed',
    color: 'hsl(0, 84%, 60%)', // Vibrant Red
  },
} satisfies ChartConfig;

const weeklyCallChartConfig = {
  calls: {
    label: 'Calls',
    color: 'hsl(24, 95%, 50%)', // Vibrant Orange
  },
} satisfies ChartConfig;

// Generate sample data for the last 30 days (from May 14, 2025 to June 12, 2025)
const generateLast30DaysData = () => {
  const data = [];
  const today = new Date(2025, 5, 12); // June 12, 2025
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    data.push({
      // Format as "Mon DD", e.g., "May 14"
      date: date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      }),
      calls: Math.floor(Math.random() * (60 - 10 + 1)) + 10, // Random calls between 10 and 60
    });
  }
  return data;
};

const dailyCallData = generateLast30DaysData();

const dailyCallChartConfig = {
  calls: {
    label: 'Calls',
    color: 'hsl(24, 95%, 50%)', // Vibrant Orange
  },
} satisfies ChartConfig;

export default function DashboardPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
        <div className="mt-2 sm:mt-0">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,248</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Call Duration
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3m 42s</div>
            <p className="text-xs text-muted-foreground">-8% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staff Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+2 new this month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clinics</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">+1 new this month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Calls Today</CardTitle>
            <CardDescription>
              Distribution of answered vs missed calls
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={callTypeChartConfig}
              className="mx-auto aspect-square max-h-[300px]"
            >
              <PieChart>
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
                <Pie
                  data={callTypeData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                >
                  {callTypeData.map((entry) => (
                    <Cell key={entry.name} fill={entry.fill} /> // Cell fill now uses CSS variables
                  ))}
                </Pie>
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Call Analytics</CardTitle>
            <CardDescription>Call volume over the past week</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ChartContainer
              config={weeklyCallChartConfig}
              className="h-[300px] w-full"
            >
              <BarChart data={callData} accessibilityLayer>
                <CartesianGrid
                  vertical={false}
                  strokeDasharray="3 3"
                  stroke="var(--muted)"
                />
                <XAxis
                  dataKey="name"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <YAxis tickLine={false} axisLine={false} tickMargin={8} />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator="dot" />}
                />
                <Bar
                  dataKey="calls"
                  fill="var(--color-calls)"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="lg:col-span-7">
          <CardHeader>
            <CardTitle>Daily Call Volume (Last 30 Days)</CardTitle>
            <CardDescription>
              Showing total calls over the past 30 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={dailyCallChartConfig}
              className="h-[300px] w-full"
            >
              <AreaChart
                accessibilityLayer
                data={dailyCallData}
                margin={{
                  left: 12,
                  right: 12,
                  top: 5,
                  bottom: 0,
                }}
              >
                <CartesianGrid
                  vertical={false}
                  strokeDasharray="3 3"
                  stroke="var(--muted)"
                />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  // Optional: format ticks if they are too crowded.
                  // tickFormatter={(value, index) => {
                  //   // Show every Nth label to prevent overlap
                  //   const N = 5; // Show every 5th label
                  //   return index % N === 0 ? value : '';
                  // }}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator="line" />}
                />
                <defs>
                  <linearGradient id="fillCalls" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--color-calls)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--color-calls)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <Area
                  dataKey="calls"
                  type="natural"
                  fill="url(#fillCalls)"
                  fillOpacity={0.4}
                  stroke="var(--color-calls)"
                  stackId="a"
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
          <CardFooter>
            <div className="flex w-full items-start gap-2 text-sm">
              <div className="grid gap-2">
                <div className="flex items-center gap-2 font-medium leading-none">
                  Trending up <TrendingUp className="h-4 w-4" />
                </div>
                <div className="text-muted-foreground flex items-center gap-2 leading-none">
                  Data for the past 30 days
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
      />
    </div>
  );
}
