'use client';

import { useState, useEffect } from 'react';
import { Building2, Plus, Search, AlertCircle, Loader2, X } from 'lucide-react';
import { fetchAuthSession } from 'aws-amplify/auth';
import { toast } from 'sonner';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { ClinicType } from '@/lib/types';
import {
  getUserClinics,
  createClinic,
  updateClinic, ClinicCreationData
} from '@/services/clinicService';

const clinicFormSchema = z.object({
  clinic_name: z.string().min(1, 'Clinic name is required'),
  clinic_email: z.string().email('Please enter a valid email address'),
  clinic_website: z.string().url('Please enter a valid website URL'),
  clinic_phone: z.string().optional(),
});

export default function ManageClinicsPage() {
  // const router = useRouter();
  const [clinics, setClinics] = useState<ClinicType[]>([]);
  const [filteredClinics, setFilteredClinics] = useState<ClinicType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [duplicateError, setDuplicateError] = useState<string | null>(null);
  const [editingClinic, setEditingClinic] = useState<ClinicType | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  const form = useForm<z.infer<typeof clinicFormSchema>>({
    resolver: zodResolver(clinicFormSchema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_website: '',
      clinic_phone: '',
    },
  });

  useEffect(() => {
    fetchClinics();
  }, []);

  useEffect(() => {
    const filtered = clinics.filter(
      (clinic) =>
        clinic.clinic_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        clinic.clinic_email.toLowerCase().includes(searchTerm.toLowerCase()),
    );
    setFilteredClinics(filtered);
  }, [clinics, searchTerm]);

  const fetchClinics = async () => {
    try {
      setLoading(true);
      setError(null);

      const session = await fetchAuthSession();
      const accessToken = session.tokens?.accessToken?.toString();

      if (!accessToken) {
        setError('Authentication required');
        return;
      }

      const result = await getUserClinics(accessToken);

      if (result.ok && result.data) {
        setClinics(result.data);
      } else {
        setError(result.error || 'Failed to fetch clinics');
      }
    } catch (err) {
      console.error('Error fetching clinics:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof clinicFormSchema>) => {
    if (editingClinic) {
      setIsUpdating(true);
    } else {
      setIsCreating(true);
    }
    setDuplicateError(null);

    try {
      const session = await fetchAuthSession();
      const accessToken = session.tokens?.accessToken?.toString();

      if (!accessToken) {
        toast.error('Authentication required');
        return;
      }

      const clinicData: ClinicCreationData = {
        clinic_name: values.clinic_name,
        clinic_email: values.clinic_email,
        clinic_website: values.clinic_website,
        clinic_phone: values.clinic_phone,
      };

      let result;
      if (editingClinic) {
        result = await updateClinic(editingClinic._id, clinicData, accessToken);
      } else {
        result = await createClinic(clinicData, accessToken);
      }

      if (result.ok) {
        toast.success(
          editingClinic
            ? 'Clinic updated successfully'
            : 'Clinic created successfully',
        );
        setIsDialogOpen(false);
        form.reset();
        setEditingClinic(null);
        fetchClinics(); // Refresh the list
      } else {
        if (result.error?.includes('already exists')) {
          setDuplicateError(result.error);
        } else {
          toast.error(
            result.error ||
              `Failed to ${editingClinic ? 'update' : 'create'} clinic`,
          );
        }
      }
    } catch (error) {
      console.error(
        `Error ${editingClinic ? 'updating' : 'creating'} clinic:`,
        error,
      );
      toast.error('An unexpected error occurred');
    } finally {
      setIsCreating(false);
      setIsUpdating(false);
    }
  };

  const handleEdit = (clinicId: string) => {
    const clinic = clinics.find((c) => c._id === clinicId);
    if (clinic) {
      setEditingClinic(clinic);
      form.setValue('clinic_name', clinic.clinic_name);
      form.setValue('clinic_email', clinic.clinic_email);
      form.setValue('clinic_website', clinic.clinic_website);
      form.setValue('clinic_phone', clinic.clinic_phone || '');
      setIsDialogOpen(true);
    }
  };

  // const handleRemove = async (clinicId: string) => {
  //   try {
  //     const session = await fetchAuthSession();
  //     const accessToken = session.tokens?.accessToken?.toString();

  //     if (!accessToken) {
  //       toast.error('Authentication required');
  //       return;
  //     }

  //     const result = await deactivateClinic(clinicId, accessToken);

  //     if (result.ok) {
  //       toast.success('Clinic removed successfully');
  //       fetchClinics(); // Refresh the list
  //     } else {
  //       toast.error(result.error || 'Failed to remove clinic');
  //     }
  //   } catch (error) {
  //     console.error('Error removing clinic:', error);
  //     toast.error('An unexpected error occurred');
  //   }
  // };

  // const handleGoToAdminPanel = (clinicId: string) => {
  //   router.push(`/admin/clinics/${clinicId}`);
  // };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manage Clinics</h1>
          <p className="text-muted-foreground">
            Monitor and manage the details of every clinic you operate.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              setIsDialogOpen(open);
              if (!open) {
                setEditingClinic(null);
                form.reset();
                setDuplicateError(null);
              }
            }}
          >
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add New Clinics
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingClinic ? 'Edit Clinic' : 'Add New Clinic'}
                </DialogTitle>
                <DialogDescription>
                  {editingClinic
                    ? 'Update the clinic information below.'
                    : 'Quickly add a new clinic by filling in basic information.'}
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="clinic_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic name</FormLabel>
                        <FormControl>
                          <Input placeholder="AIIMS" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="clinic_website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic website</FormLabel>
                        <FormControl>
                          <Input placeholder="www.clinic.com" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="clinic_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="clinic_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone number</FormLabel>
                        <FormControl>
                          <Input placeholder="+91 98765 43112" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {duplicateError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="flex items-center justify-between">
                          <span>This clinic already exists in the system.</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDuplicateError(null)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-sm mt-1">
                          A clinic with these details already exists in our
                          records.
                        </p>
                      </AlertDescription>
                    </Alert>
                  )}

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isCreating || isUpdating}>
                      {isCreating || isUpdating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {editingClinic ? 'Updating...' : 'Adding...'}
                        </>
                      ) : editingClinic ? (
                        'Update Clinic'
                      ) : (
                        'Add Clinic'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search Staff..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline" size="sm">
          <Search className="mr-2 h-4 w-4" />
          Search
        </Button>
      </div>

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Clinic Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredClinics.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <Building2 className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">
                      {searchTerm
                        ? 'No clinics found matching your search.'
                        : 'No clinics found.'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredClinics.map((clinic) => (
                <TableRow key={clinic._id}>
                  <TableCell className="font-medium">
                    {clinic.clinic_name}
                  </TableCell>
                  <TableCell>{clinic.clinic_email}</TableCell>
                  <TableCell>
                    <Badge variant={clinic.is_active ? 'success' : 'pending'}>
                      {clinic.is_active ? 'Active' : 'Removed'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(clinic._id)}
                      >
                        Edit
                      </Button>
                      {/* <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemove(clinic._id)}
                      >
                        Remove
                      </Button> */}

                      {/* <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGoToAdminPanel(clinic._id)}
                      >
                        Go to Admin Panel
                      </Button> */}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
